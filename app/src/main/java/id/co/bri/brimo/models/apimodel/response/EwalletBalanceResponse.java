package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;

public class EwalletBalanceResponse {
    @SerializedName("type")
    @Expose
    private String type;
    @SerializedName("value_string")
    @Expose
    private String value_string;
    @SerializedName("value")
    @Expose
    private Long value;
    @SerializedName("status")
    @Expose
    private Integer status;

    public EwalletBalanceResponse(String type, String value_string, Long value, Integer status) {
        this.type = type;
        this.value_string = value_string;
        this.value = value;
        this.status = status;
    }

    public EwalletBalanceResponse() {
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue_string() {
        return value_string;
    }

    public void setValue_string(String value_string) {
        this.value_string = value_string;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
